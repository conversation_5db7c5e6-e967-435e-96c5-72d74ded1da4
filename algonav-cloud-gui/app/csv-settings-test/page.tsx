'use client';

import { useState, useCallback } from 'react';
import { Box, Button, Container, Paper, Typography, Divider } from '@mui/material';
import LogConfigInput from '../../components/inputs/LogConfigInput';

export default function CsvSettingsTest() {
  const [logConfig, setLogConfig] = useState({
    label: 'AlgoNav\'s Tighly-Coupling solution',
    trigger: 'RTS_TIME',
    dt: 0.1,
    fields: [],
    csv_settings: {
      delim: ", ",
      delim_sub: ",",
      eol: "\n",
      delimiter_after_last_col: false,
      output_full_header: true,
      output_header_row: true,
      header_prefix: "# ",
    }
  });

  const [jsonOutput, setJsonOutput] = useState('');

  // Use useCallback to prevent unnecessary re-renders
  const handleChange = useCallback((newValue: any) => {
    setLogConfig(prevConfig => {
      // Only update if the value has actually changed
      if (JSON.stringify(prevConfig) !== JSON.stringify(newValue)) {
        return newValue;
      }
      return prevConfig;
    });
  }, []);

  const handleShowJson = () => {
    setJsonOutput(JSON.stringify(logConfig, null, 2));
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        CSV Settings Test
      </Typography>
      <Typography variant="body1" paragraph>
        This page demonstrates the LogConfigInput component with CSV settings.
      </Typography>

      <Paper sx={{ p: 3, mb: 4 }}>
        <LogConfigInput
          value={logConfig}
          onChange={handleChange}
          name="logConfig"
          gui={{
            label: 'Log Configuration',
            tooltip: 'Configure the log output',
            availableLogGroups: [
              { name: 'nav', label: 'Navigation', logprefix: 'nav' },
              { name: 'imu', label: 'IMU', logprefix: 'imu' },
              { name: 'gnss', label: 'GNSS', logprefix: 'gnss' },
            ],
            availableSensors: [
              { name: 'dgnssX', label: 'DGNSS X', logprefix: 'dgnssX' },
              { name: 'dgnssY', label: 'DGNSS Y', logprefix: 'dgnssY' },
            ],
          }}
          template_data={{
            available_log_groups: [
              { name: 'nav', label: 'Navigation', logprefix: 'nav' },
              { name: 'imu', label: 'IMU', logprefix: 'imu' },
              { name: 'gnss', label: 'GNSS', logprefix: 'gnss' },
            ],
            available_sensors: [
              { name: 'dgnssX', label: 'DGNSS X', logprefix: 'dgnssX' },
              { name: 'dgnssY', label: 'DGNSS Y', logprefix: 'dgnssY' },
            ],
          }}
        />
      </Paper>

      <Box sx={{ mb: 4 }}>
        <Button variant="contained" onClick={handleShowJson}>
          Show JSON Output
        </Button>
      </Box>

      {jsonOutput && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            JSON Output
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <pre style={{ whiteSpace: 'pre-wrap', overflow: 'auto', maxHeight: '400px' }}>
            {jsonOutput}
          </pre>
        </Paper>
      )}
    </Container>
  );
}
