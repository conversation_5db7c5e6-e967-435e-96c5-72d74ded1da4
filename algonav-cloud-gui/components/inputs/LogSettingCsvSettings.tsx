// components/inputs/LogSettingCsvSettings.tsx
import { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Box,
  Typography,
  Divider,
  FormControlLabel,
  Switch,
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface LogSettingCsvSettingsProps {
  value: {
    delim?: string;
    delim_sub?: string;
    eol?: string;
    delimiter_after_last_col?: boolean;
    output_full_header?: boolean;
    output_header_row?: boolean;
    comment_prefix?: string;
  };
  onChange: (value: any) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    [key: string]: any;
  };
}

// Default values for CSV settings
const defaultCsvSettings = {
  delim: ", ",
  delim_sub: ",",
  eol: "\n",
  delimiter_after_last_col: false,
  output_full_header: true,
  output_header_row: true,
  comment_prefix: "# ",
};

// Helper functions to handle special characters
const unescapeSpecialChars = (str: string): string => {
  if (!str) return str;
  // Convert escaped sequences to actual characters
  return str
    .replace(/\\n/g, '\n')
    .replace(/\\r/g, '\r')
    .replace(/\\t/g, '\t');
};

const escapeSpecialChars = (str: string): string => {
  if (!str) return str;
  // Convert actual characters to visible escaped sequences
  return str
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t');
};

export default function LogSettingCsvSettings({
  value = defaultCsvSettings,
  onChange,
  name,
  gui,
}: LogSettingCsvSettingsProps) {
  // Process incoming values to display escaped versions
  const processIncomingValues = (incomingValue: any) => {
    const processed = { ...defaultCsvSettings, ...incomingValue };

    // Escape special characters for display in all text fields that might contain them
    if (processed.eol) {
      processed.eol = escapeSpecialChars(processed.eol);
    }
    if (processed.delim) {
      processed.delim = escapeSpecialChars(processed.delim);
    }
    if (processed.delim_sub) {
      processed.delim_sub = escapeSpecialChars(processed.delim_sub);
    }
    if (processed.comment_prefix) {
      processed.comment_prefix = escapeSpecialChars(processed.comment_prefix);
    }

    return processed;
  };

  // Initialize with provided value or defaults (with escaped special chars)
  const [localValues, setLocalValues] = useState<typeof defaultCsvSettings>(
    processIncomingValues(value)
  );

  useEffect(() => {
    // Update local state when props change (with escaped special chars)
    setLocalValues(processIncomingValues(value));
  }, [value]);

  // Handle text field changes
  const handleTextChange = (field: keyof typeof defaultCsvSettings) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newValues = { ...localValues, [field]: e.target.value };
    setLocalValues(newValues);

    // Create a copy for output with unescaped special characters
    const outputValues = { ...newValues };

    // Fields that might contain special characters need to be unescaped before sending to parent
    const fieldsWithSpecialChars = ['eol', 'delim', 'delim_sub', 'comment_prefix'];

    if (fieldsWithSpecialChars.includes(field)) {
      outputValues[field] = unescapeSpecialChars(e.target.value);
      onChange(outputValues);
    } else {
      onChange(newValues);
    }
  };

  // Handle switch/boolean changes
  const handleBooleanChange = (field: keyof typeof defaultCsvSettings) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newValues = { ...localValues, [field]: e.target.checked };
    setLocalValues(newValues);
    onChange(newValues);
  };

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}

      <Stack spacing={3}>
        {/* Delimiters Section */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Delimiters
          </Typography>
          <Stack spacing={2}>
            {/* First row: Field and Sub-field delimiters */}
            <Stack direction="row" spacing={2} alignItems="flex-start">
              <TextField
                size="small"
                label="Field Delimiter"
                value={localValues.delim}
                onChange={handleTextChange('delim')}
                placeholder="', '"
                helperText="Between fields"
                sx={{ width: '140px' }}
              />
              <TextField
                size="small"
                label="Sub-field Delimiter"
                value={localValues.delim_sub}
                onChange={handleTextChange('delim_sub')}
                placeholder="','"
                helperText="Between columns"
                sx={{ width: '140px' }}
              />
              <TextField
                size="small"
                label="End of Line"
                value={localValues.eol}
                onChange={handleTextChange('eol')}
                placeholder="\n"
                helperText="Line ending"
                sx={{ width: '140px' }}
              />
            </Stack>
            <FormControlLabel
              control={
                <Switch
                  checked={localValues.delimiter_after_last_col}
                  onChange={handleBooleanChange('delimiter_after_last_col')}
                />
              }
              label="Add delimiter after last column"
            />
          </Stack>
        </Box>

        <Divider />

        {/* Header Section */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Header Options
          </Typography>
          <Stack spacing={2}>
            {/* Header switches in a row */}
            <Stack direction="row" spacing={3} alignItems="center">
              <FormControlLabel
                control={
                  <Switch
                    checked={localValues.output_full_header}
                    onChange={handleBooleanChange('output_full_header')}
                  />
                }
                label="Output full header"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={localValues.output_header_row}
                    onChange={handleBooleanChange('output_header_row')}
                  />
                }
                label="Output header row"
              />
            </Stack>
            {/* Comment prefix field */}
            <TextField
              size="small"
              label="Prefix for header and comments"
              value={localValues.comment_prefix}
              onChange={handleTextChange('comment_prefix')}
              placeholder="# "
              helperText="Prefix for header and comment lines"
              sx={{ width: '140px' }}
            />
          </Stack>
        </Box>
      </Stack>
    </FormControl>
  );
}
