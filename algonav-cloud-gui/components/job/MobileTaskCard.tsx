import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  Stack,
  IconButton,
  Divider,
  Box,
  Grid,
  Tooltip,
  CircularProgress
} from '@mui/material';
import SupportIcon from '../common/SupportIcon';
import DownloadIcon from '../common/DownloadIcon';
import { Task } from '../../types/job';
import { formatDate, getStatusDisplay, getFileTypeLabel } from '../../utils/jobUtils';

interface MobileTaskCardProps {
  task: Task;
  onDownloadFile: (taskId: number, result: any) => void;
  onDownloadTaskFiles: (taskId: number) => void;
  onOpenSupportDialog: (taskId: number, datasetName: string, jobId: string) => void; // Changed batchId to jobId
  downloadingFiles: Record<string, boolean>;
  jobId: string; // Changed batchId to jobId
}

const MobileTaskCard: React.FC<MobileTaskCardProps> = ({
  task,
  onDownloadFile,
  onDownloadTaskFiles,
  onOpenSupportDialog,
  downloadingFiles,
  jobId // Changed batchId to jobId
}) => {
  const fileTypes = ['kml', 'csv', 'tix', 'pdf'];
  const status = getStatusDisplay(task.status, true);
  
  return (
    <Card sx={{ mb: 2, display: { xs: 'block', md: 'none' } }}>
      <CardContent>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
          <Typography variant="h6" component="div">
            Task #{task.id}
          </Typography>
          <Chip
            label={status.text}
            color={status.color as 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | undefined}
            size="small"
            sx={{ fontWeight: 500 }}
          />
        </Stack>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {task.dataset?.name || 'N/A'}
        </Typography>
        
        <Typography variant="body2" sx={{ mb: 1 }}>
          Created: {formatDate(task.created_at)}
        </Typography>
        
        <Divider sx={{ my: 1 }} />
        
        <Typography variant="subtitle2" sx={{ mt: 1, mb: 1 }}>
          Available Files:
        </Typography>
        
        <Grid container spacing={1}>
          {fileTypes.map((type) => {
            const result = task.task_results?.find(r => 
              type === 'csv' ? 
                r.file_type?.toLowerCase() === 'csv_pva' :
                r.file_type?.toLowerCase() === type
            );
            
            if (!result) return null;
            
            const fileId = `${task.id}-${result.id}`;
            const isDownloading = downloadingFiles[fileId];
            
            return (
              <Grid item key={type}>
                <Tooltip title={`Download ${getFileTypeLabel(type)}`}>
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    alignItems: 'center'
                  }}>
                    <IconButton
                      size="small"
                      onClick={() => onDownloadFile(task.id, result)}
                      disabled={isDownloading}
                      aria-label={`Download ${getFileTypeLabel(type)}`}
                    >
                      {isDownloading ? (
                        <CircularProgress size={20} />
                      ) : (
                        <DownloadIcon fileType={type} isMulti={false} />
                      )}
                    </IconButton>
                    <Typography variant="caption">
                      {getFileTypeLabel(type)}
                    </Typography>
                  </Box>
                </Tooltip>
              </Grid>
            );
          })}
        </Grid>
      </CardContent>
      
      <CardActions sx={{ justifyContent: 'flex-end', gap: 1 }}>
        {task.task_results && task.task_results.length > 0 && (
          <Tooltip title="Download all files for this task">
            <IconButton
              size="medium"
              onClick={() => onDownloadTaskFiles(task.id)}
              disabled={!!downloadingFiles[`task-${task.id}`]}
              aria-label={`Download all files for task ${task.id}`}
              sx={{ 
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main'
                }
              }}
            >
              {downloadingFiles[`task-${task.id}`] ? (
                <CircularProgress size={20} />
              ) : (
                <DownloadIcon fileType="all" isMulti={false} />
              )}
            </IconButton>
          </Tooltip>
        )}
        <Tooltip title="Request support for this task">
          <IconButton
            size="small"
            onClick={() => onOpenSupportDialog(task.id, task.dataset?.name || '', jobId)} // Changed batchId to jobId
            aria-label={`Request support for task ${task.id}`}
            sx={{ color: 'text.secondary' }}
          >
            <SupportIcon width={20} height={20} />
          </IconButton>
        </Tooltip>
      </CardActions>
    </Card>
  );
};

export default MobileTaskCard;
