import { useState, useCallback } from 'react';
import { BulkJobError, isBulkJobError } from '@/lib/errors/BulkJobError';
import useJobStatus from './useJobStatus';

interface UseBulkJobOptions {
  onComplete?: (jobId: string) => void;
  onError?: (error: Error) => void;
}

interface BulkJobState {
  jobId: string | null;
  isProcessing: boolean;
  error: Error | null;
}

export function useBulkJob(options: UseBulkJobOptions = {}) {
  const [state, setState] = useState<BulkJobState>({
    jobId: null,
    isProcessing: false,
    error: null
  });

  // Use job status hook for polling
  const { status } = useJobStatus(state.jobId, {
    onComplete: (jobId) => {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: null
      }));
      options.onComplete?.(jobId);
    },
    onError: (error) => {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error
      }));
      options.onError?.(error);
    }
  });

  const createMultiKML = useCallback(async (taskIds: number[]) => {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        error: null
      }));

      const response = await fetch('/api/tasks/bulk/kml', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskIds }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw isBulkJobError(data) 
          ? new BulkJobError(data.code, data.message, data.details)
          : new Error(data.error || 'Failed to create Multi-KML job');
      }

      setState(prev => ({
        ...prev,
        jobId: data.job.id
      }));

      return data.job;
    } catch (error) {
      const finalError = isBulkJobError(error) 
        ? error 
        : BulkJobError.unknown(error);

      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: finalError
      }));

      options.onError?.(finalError);
      throw finalError;
    }
  }, [options]);

  const reset = useCallback(() => {
    setState({
      jobId: null,
      isProcessing: false,
      error: null
    });
  }, []);

  return {
    createMultiKML,
    reset,
    jobId: state.jobId,
    isProcessing: state.isProcessing || status === 'processing',
    error: state.error,
    status
  };
}

export default useBulkJob;