import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from '../services/api'

export function useTemplates() {
  return useQuery({
      queryKey: ['templates'],
      queryFn: api.getTemplates,
      retry: 2
  })
}

export function useCreateTemplate() {
  const queryClient = useQueryClient()
    
  return useMutation({
      mutationFn: api.createTemplate,
      onMutate: async (newTemplate) => {
          await queryClient.cancelQueries({ queryKey: ['templates'] })
          const previousTemplates = queryClient.getQueryData(['templates'])
            
          queryClient.setQueryData(['templates'], (old: any) => ({
              ...old,
              data: [...(old?.data || []), { ...newTemplate, id: 'temp-id' }]
          }))

          return { previousTemplates }
      },
      onError: (err, _, context) => {
          queryClient.setQueryData(['templates'], context.previousTemplates)
          return err
      },
      onSettled: () => {
          queryClient.invalidateQueries({ queryKey: ['templates'] })
      }
  })
}


export function useDeleteTemplate() {
  const queryClient = useQueryClient()
    
  return useMutation({
      mutationFn: api.deleteTemplate,
      onMutate: async (deletedId) => {
          await queryClient.cancelQueries({ queryKey: ['templates'] })
          const previousTemplates = queryClient.getQueryData(['templates'])
            
          queryClient.setQueryData(['templates'], (old: any) => ({
              ...old,
              data: old.data.filter(template => template.id !== deletedId)
          }))

          return { previousTemplates }
      },
      onError: (err, _, context) => {
          queryClient.setQueryData(['templates'], context.previousTemplates)
          return err
      },
      onSettled: () => {
          queryClient.invalidateQueries({ queryKey: ['templates'] })
      }
  })
}