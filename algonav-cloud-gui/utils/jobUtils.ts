/**
 * Format a date string according to US locale and display rules with 24-hour time
 */
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (date.toDateString() === today.toDateString()) {
    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
  }
  if (date.toDateString() === yesterday.toDateString()) {
    return `Yesterday, ${date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })}`;
  }
  return date.toLocaleString('en-US', { 
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

/**
 * Format a job name for display (formerly batch name)
 */
export const formatJobName = (name) => {
  // Handle both date string and name formats
  if (name instanceof Date || !isNaN(Date.parse(name))) {
    const date = new Date(name);
    return `Job ${date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).replace(',', '')}`;
  }

  // Handle existing job name format (formerly batch)
  const match = name.match(/(Batch|Job) (\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}):\d{2}\.\d{3}Z/); // Allow both for transition if needed, but format as Job
  if (match) {
    const [, , date, time] = match; // Adjusted index due to added group
    return `Job ${date.replace(/-/g, '.')} ${time}`;
  }
  return name;
};

/**
 * Get the display label for a file type
 */
export const getFileTypeLabel = (fileType) => {
  switch (fileType?.toLowerCase()) {
    case 'kml':
      return 'KML';
    case 'csv_pva':
    case 'csv':
      return 'CSV';
    case 'tix':
      return 'TIX';
    case 'pdf':
      return 'PDF';
    default:
      return fileType?.toUpperCase() || 'Download';
  }
};

/**
 * Get status display information including text, color, and tooltip
 */
export const getStatusDisplay = (status, isTask = false) => {
  // Handle task-specific statuses
  if (isTask) {
    switch (status?.toLowerCase()) {
      case 'queued':
        return { text: 'Queued', color: 'default' };
      case 'processing':
        return { text: 'Processing', color: 'info' };
      case 'completed':
      case 'success':
        return { text: 'Completed', color: 'success' };
      case 'failed':
      case 'error':
        return { text: 'Failed', color: 'error' };
      default:
        return { text: status || 'Unknown', color: 'default' };
    }
  }

  // Handle batch statuses (x/y/z format)
  const parts = status?.split('/');
  if (parts?.length === 3) {
    const [completed, failed, total] = parts.map(Number);
    const remaining = total - (completed + failed);
    
    if (completed === total && failed === 0) {
      return { text: 'Completed', color: 'success' };
    }
    
    if ((completed + failed) === total) {
      if (failed === total) {
        return { text: 'Failed', color: 'error' };
      }
      return {
        text: 'Partially Completed',
        tooltip: `${completed} completed, ${failed} failed out of ${total} total`,
        color: 'warning'
      };
    }
    
    return {
      text: 'In Progress',
      tooltip: `${completed} completed, ${failed} failed, ${remaining} remaining`,
      color: 'info'
    };
  }
  
  // Handle simple status strings for batches
  if (status === 'queued') return { text: 'Queued', color: 'default' };
  if (status === 'processing') return { text: 'Processing', color: 'info' };
  
  return { text: status || 'Unknown', color: 'default' };
};

/**
 * Check if files of a specific type exist in the tasks
 */
export const hasFilesOfType = (tasks, type) => {
  return tasks.some(task =>
    task.task_results?.some(result =>
      type === 'csv' ?
        result.file_type?.toLowerCase() === 'csv_pva' :
        result.file_type?.toLowerCase() === type
    )
  );
};